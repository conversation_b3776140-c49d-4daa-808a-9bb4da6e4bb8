import { useColorScheme } from '@/hooks/useColorScheme';
import { Bill } from '@/types/bill';
import { printBill, shareBill } from '@/utils/billUtils';
import { DateFilterPreset, getDateRangeFromPreset } from '@/utils/dateUtils';
import { deleteBill, getBills, updateBill } from '@/utils/storage';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useFocusEffect, useNavigation } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useCallback, useState } from 'react';
import { Alert, FlatList, SafeAreaView, StyleSheet, View, TouchableOpacity, Platform } from 'react-native';
import Constants from 'expo-constants';
import { ActivityIndicator, Surface, Text, TouchableRipple, useTheme } from 'react-native-paper';
import { DatePickerModal, en, registerTranslation } from 'react-native-paper-dates';
import DateFilterChips from '@/components/history/DateFilterChips';
import BillCard from '@/components/history/BillCard';
import BillDetailDialog from '@/components/history/BillDetailDialog';
import EditNameDialog from '@/components/history/EditNameDialog';
import SearchHeader from '@/components/history/SearchHeader';

registerTranslation('en', en);

export default function HistoryScreen() {
  const theme = useTheme();
  const colorScheme = useColorScheme();
  const navigation = useNavigation();
  const [bills, setBills] = useState<Bill[]>([]);
  const [filteredBills, setFilteredBills] = useState<Bill[]>([]);
  const [selectedBill, setSelectedBill] = useState<Bill | null>(null);
  const [detailDialogVisible, setDetailDialogVisible] = useState(false);
  const [editNameDialogVisible, setEditNameDialogVisible] = useState(false);
  const [editedName, setEditedName] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [dateRange, setDateRange] = useState<{
    startDate: Date | undefined;
    endDate: Date | undefined;
  }>({
    startDate: undefined,
    endDate: undefined,
  });
  const [activeDatePreset, setActiveDatePreset] = useState<DateFilterPreset>('all');

  const fetchBills = useCallback(async () => {
    setIsLoading(true);
    try {
      const loadedBills = await getBills();
      loadedBills.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
      setBills(loadedBills);
    } catch (error) {
      console.error('Error loading bills:', error);
      Alert.alert('Error', 'Failed to load bill history. Please try again.');
      setBills([]);
      setFilteredBills([]);
    }
    setIsLoading(false);
  }, []);

  useFocusEffect(
    useCallback(() => {
      fetchBills();
      return () => { /* Optional cleanup */ };
    }, [fetchBills])
  );

  // Use shared utility function for date range calculation
  const getDateRangeForFilter = (preset: DateFilterPreset): { start?: Date, end?: Date } => {
    // Use the shared utility, passing the custom date range when needed
    return getDateRangeFromPreset(preset, preset === 'custom' ? dateRange : undefined);
  };

  // Select a preset date filter
  const selectDatePreset = (preset: DateFilterPreset) => {
    setActiveDatePreset(preset);

    if (preset === 'custom') {
      // For custom, just show the date picker
      setDatePickerVisible(true);
      return;
    }

    // For other presets, apply the filter with the generated date range
    const range = getDateRangeForFilter(preset);
    const newDateRange = {
      startDate: range.start,
      endDate: range.end,
    };
    setDateRange(newDateRange);
    applyFilters(searchQuery, range.start, range.end, bills);
  };

  // Memoize filter function with useMemo for better performance
  const filteredBillsList = React.useMemo(() => {
    let tempFilteredBills = [...bills];

    // Apply search query filter
    if (searchQuery.trim() !== '') {
      const lowerQuery = searchQuery.toLowerCase();
      tempFilteredBills = tempFilteredBills.filter(
        (bill) =>
          (bill.name && bill.name.toLowerCase().includes(lowerQuery)) ||
          bill.id.includes(searchQuery) ||
          new Date(bill.date).toLocaleDateString('en-IN').includes(lowerQuery)
      );
    }

    // Apply date range filters
    if (dateRange.startDate) {
      const startOfDay = new Date(dateRange.startDate);
      startOfDay.setHours(0, 0, 0, 0);
      tempFilteredBills = tempFilteredBills.filter(bill => new Date(bill.date) >= startOfDay);
    }
    if (dateRange.endDate) {
      const endOfDay = new Date(dateRange.endDate);
      endOfDay.setHours(23, 59, 59, 999);
      tempFilteredBills = tempFilteredBills.filter(bill => new Date(bill.date) <= endOfDay);
    }

    return tempFilteredBills;
  }, [bills, searchQuery, dateRange.startDate, dateRange.endDate]);

  // Update filteredBills state whenever the memoized result changes
  React.useEffect(() => {
    setFilteredBills(filteredBillsList);
  }, [filteredBillsList]);

  // Simple function to apply filters from non-memoized contexts (like initial load)
  const applyFilters = (query: string, sDate?: Date, eDate?: Date, billsToFilter?: Bill[]) => {
    setSearchQuery(query);
    setDateRange({
      startDate: sDate,
      endDate: eDate
    });

    // If we need immediate results (e.g., during initial load), calculate directly
    if (billsToFilter) {
      let tempFilteredBills = [...billsToFilter];

      if (query.trim() !== '') {
        const lowerQuery = query.toLowerCase();
        tempFilteredBills = tempFilteredBills.filter(
          (bill) =>
            (bill.name && bill.name.toLowerCase().includes(lowerQuery)) ||
            bill.id.includes(query) ||
            new Date(bill.date).toLocaleDateString('en-IN').includes(lowerQuery)
        );
      }

      if (sDate) {
        const startOfDay = new Date(sDate);
        startOfDay.setHours(0, 0, 0, 0);
        tempFilteredBills = tempFilteredBills.filter(bill => new Date(bill.date) >= startOfDay);
      }
      if (eDate) {
        const endOfDay = new Date(eDate);
        endOfDay.setHours(23, 59, 59, 999);
        tempFilteredBills = tempFilteredBills.filter(bill => new Date(bill.date) <= endOfDay);
      }
      setFilteredBills(tempFilteredBills);
    }
  };

  const handleSearchChange = (query: string) => {
    // Just update the search query state - our useMemo will handle filtering
    setSearchQuery(query);
  };

  const onDismissDatePicker = useCallback(() => {
    setDatePickerVisible(false);
  }, []);

  const onConfirmDateRange = useCallback(
    (params: { startDate: Date | undefined, endDate: Date | undefined }) => {
      setDatePickerVisible(false);
      setDateRange(params);
      setActiveDatePreset('custom');
      applyFilters(searchQuery, params.startDate, params.endDate);
    },
    [searchQuery]
  );

  const handleClearDateFilter = () => {
    const emptyRange = { startDate: undefined, endDate: undefined };
    setDateRange(emptyRange);
    setActiveDatePreset('all');
    applyFilters(searchQuery, undefined, undefined);
  };

  const handleDeleteBill = async (billId: string) => {
    Alert.alert(
      'Confirm Deletion',
      'Are you sure you want to delete this bill? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteBill(billId);
              const loadedBills = await getBills();
              loadedBills.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
              setBills(loadedBills);

              if (selectedBill && selectedBill.id === billId) {
                setDetailDialogVisible(false);
                setSelectedBill(null);
              }
              Alert.alert('Success', 'Bill deleted successfully.');
            } catch (error) {
              console.error('Error deleting bill:', error);
              Alert.alert('Error', 'Failed to delete bill.');
            }
          },
        },
      ]
    );
  };

  const handleViewBill = (bill: Bill) => {
    setSelectedBill(bill);
    setDetailDialogVisible(true);
  };

  const handlePrintBill = async (bill: Bill) => {
    try {
      await printBill(bill);
    } catch (error) {
      console.error('Error printing bill:', error);
      Alert.alert('Error', 'Failed to print bill');
    }
  };

  const handleShareBill = async (bill: Bill) => {
    try {
      await shareBill(bill);
    } catch (error) {
      console.error('Error sharing bill:', error);
      Alert.alert('Error', 'Failed to share bill');
    }
  };

  const handleEditName = (bill: Bill) => {
    setSelectedBill(bill);
    setEditedName(bill.name || '');
    setEditNameDialogVisible(true);
  };

  const handleSaveName = async () => {
    if (!selectedBill) return;

    try {
      const updatedBillData = {
        ...selectedBill,
        name: editedName.trim() || `Bill #${selectedBill.id.slice(-4)}`,
      };

      await updateBill(updatedBillData);

      const reloadedBills = await getBills();
      reloadedBills.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
      setBills(reloadedBills);
      applyFilters(searchQuery, dateRange.startDate, dateRange.endDate, reloadedBills);

      if (detailDialogVisible) {
        setSelectedBill(updatedBillData);
      }

      setEditNameDialogVisible(false);
      Alert.alert('Success', 'Bill name updated successfully.');
    } catch (error) {
      console.error('Error updating bill name:', error);
      Alert.alert('Error', 'Failed to update bill name.');
    }
  };

  const openDrawer = () => {
    (navigation as any).openDrawer();
  };

  const componentStyles = styles(theme);

  return (
    <SafeAreaView style={[componentStyles.safeArea, { backgroundColor: theme.colors.background }]}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />

      {/* Custom Header */}
      <View style={componentStyles.customHeader}>
        <TouchableOpacity onPress={openDrawer}>
          <MaterialCommunityIcons name="menu" size={28} color={theme.colors.onPrimary} />
        </TouchableOpacity>
        <Text style={componentStyles.customHeaderTitle}>History</Text>
      </View>

      <View style={componentStyles.container}>
      {/* Header and Search Component */}
      <SearchHeader
        searchQuery={searchQuery}
        onSearchChange={handleSearchChange}
      />

      {/* Date Filter Component */}
      <DateFilterChips
        activeDatePreset={activeDatePreset}
        onSelectDatePreset={selectDatePreset}
        dateRange={dateRange}
        onClearDateFilter={handleClearDateFilter}
      />

      {/* Content Area */}
      {isLoading ? (
          <View style={componentStyles.loadingContainer}>
          <ActivityIndicator
            size="large"
            color={theme.colors.primary}
            animating={true}
          />
            <Text style={componentStyles.loadingText}>Loading bills...</Text>
        </View>
      ) : filteredBills.length === 0 ? (
          <Surface style={componentStyles.emptyContainer} elevation={0}>
          <MaterialCommunityIcons
            name="text-box-search-outline"
            size={64}
            color={theme.colors.onSurfaceDisabled}
          />
            <Text style={componentStyles.emptyText}>
            {searchQuery || dateRange.startDate || dateRange.endDate
              ? 'No bills match your search criteria.'
              : 'No bills found in your history.'}
          </Text>
          {(searchQuery || activeDatePreset !== 'all') ? (
            <TouchableRipple
              onPress={() => {
                setSearchQuery('');
                handleClearDateFilter();
              }}
                style={componentStyles.clearFiltersButton}
              borderless
            >
                <Text style={componentStyles.clearFiltersText}>Clear Filters</Text>
            </TouchableRipple>
          ) : null}
        </Surface>
      ) : (
        <FlatList
          data={filteredBills}
          renderItem={({ item }) => (
            <BillCard
              bill={item}
              onViewDetails={handleViewBill}
              onShare={handleShareBill}
              onEdit={handleEditName}
              onPrint={handlePrintBill}
              onDelete={handleDeleteBill}
            />
          )}
          keyExtractor={(item) => item.id}
          contentContainerStyle={componentStyles.listContentContainer}
          showsVerticalScrollIndicator={false}
          removeClippedSubviews={true}
          initialNumToRender={10}
          maxToRenderPerBatch={5}
          windowSize={10}
          getItemLayout={(data, index) => ({
            length: 120,
            offset: 120 * index,
            index,
          })}
        />
      )}

      {/* Date Picker Modal */}
      <DatePickerModal
        locale="en"
        mode="range"
        visible={datePickerVisible}
        onDismiss={onDismissDatePicker}
        startDate={dateRange.startDate}
        endDate={dateRange.endDate}
        onConfirm={onConfirmDateRange}
      />

      {/* Bill Detail Dialog */}
      {selectedBill && (
        <BillDetailDialog
          visible={detailDialogVisible}
          bill={selectedBill}
          onDismiss={() => setDetailDialogVisible(false)}
          onShare={handleShareBill}
          onEdit={handleEditName}
          onPrint={handlePrintBill}
          onDelete={handleDeleteBill}
        />
      )}

      {/* Edit Name Dialog */}
      <EditNameDialog
        visible={editNameDialogVisible}
        onDismiss={() => setEditNameDialogVisible(false)}
        onSave={handleSaveName}
        name={editedName}
        onChangeName={setEditedName}
      />
      </View>
    </SafeAreaView>
  );
}

const styles = (theme: any) => StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  customHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 12,
    paddingTop: Constants.statusBarHeight + 12,
    backgroundColor: theme.colors.primary,
  },
  customHeaderTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.onPrimary,
    marginLeft: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    opacity: 0.7,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    margin: 16,
    borderRadius: 16,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.7,
    maxWidth: '80%',
  },
  clearFiltersButton: {
    marginTop: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
  },
  clearFiltersText: {
    fontWeight: '600',
    color: '#2196F3',
  },
  listContentContainer: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 24,
  },
});