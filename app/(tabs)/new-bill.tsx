import { useColorScheme } from '@/hooks/useColorScheme';
import { Bill, BillItem } from '@/types/bill';
import { printBill, shareBill } from '@/utils/billUtils';
import { loadShowItemNameSetting, saveBill } from '@/utils/storage';
import { validateNumericInput, validateBillItem, validateDiscountInput } from '@/utils/inputValidation';
import { calculateItemAmount, calculateBillTotals, calculatePercentageDiscount } from '@/utils/decimalMath';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useRouter, useFocusEffect, useNavigation } from 'expo-router';
import { useIsFocused } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import { ActivityIndicator, Alert, Keyboard, SafeAreaView, ScrollView, StyleSheet, TouchableWithoutFeedback, View, Platform, TouchableOpacity } from 'react-native';
import Constants from 'expo-constants';
import {
    Appbar,
    Button,
    Divider,
    Dialog,
    FAB,
    IconButton,
    Portal,
    Surface,
    Text,
    TextInput,
    useTheme,
    RadioButton,
} from 'react-native-paper';
import Animated, { FadeInDown, SlideInRight } from 'react-native-reanimated';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Component imports
import BillTable from '@/components/bill/BillTable';
import NumberPad from '@/components/bill/NumberPad';
import BillInputField from '@/components/bill/BillInputField';

// const AnimatedSurface = Animated.createAnimatedComponent(Surface); // Comment out or remove if not used elsewhere after changes

// Simple UUID generator function
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

// Enum for active input
enum ActiveInput {
  QUANTITY = 'quantity',
  RATE = 'rate',
  DISCOUNT = 'discount', // Added for discount dialog
  NONE = 'none',
}

const STORAGE_KEYS = {
  PRINTER_DATA: 'thermal_printer_data',
};

export default function NewBillScreen() {
  const theme = useTheme();
  const colorScheme = useColorScheme();
  const router = useRouter();
  const navigation = useNavigation();
  const isFocused = useIsFocused();
  const [items, setItems] = useState<BillItem[]>([]);
  const [billName, setBillName] = useState('');
  const [isProcessingAction, setIsProcessingAction] = useState(false);

  // Input control state
  const [activeInput, setActiveInput] = useState<ActiveInput>(ActiveInput.QUANTITY);
  const [quantityInput, setQuantityInput] = useState('');
  const [rateInput, setRateInput] = useState('');

  // Discount state
  const [discountType, setDiscountType] = useState<'percentage' | 'absolute'>('percentage');
  const [discountValue, setDiscountValue] = useState('');
  const [appliedDiscount, setAppliedDiscount] = useState(0);
  const [isDiscountDialogVisible, setIsDiscountDialogVisible] = useState(false);

  // State to control item name input visibility (default: hidden)
  const [showItemNameInput, setShowItemNameInput] = useState(false);
  const [isLoadingSetting, setIsLoadingSetting] = useState(true);

  // Show report dialog
  const [showReportDialog, setShowReportDialog] = useState(false);

  // Refs for input fields - not standard focus, but for managing active state
  // const quantityInputRef = useRef<TouchableOpacity>(null); // Removed
  // const rateInputRef = useRef<TouchableOpacity>(null); // Removed

  useFocusEffect(
    useCallback(() => {
      const loadSettings = async () => {
        setIsLoadingSetting(true);
        try {
          const storedPreference = await loadShowItemNameSetting();
          setShowItemNameInput(storedPreference);
        } catch (error) {
          console.error("Failed to load item name setting:", error);
          setShowItemNameInput(false);
        } finally {
          setIsLoadingSetting(false);
        }
      };
      loadSettings();
    }, [])
  );

  useEffect(() => {
    // Set initial active input to quantity when the screen loads
    setActiveInput(ActiveInput.QUANTITY);
  }, []);

  const handleNumberPress = useCallback((num: string) => {
    if (activeInput === ActiveInput.QUANTITY) {
      setQuantityInput(prev => prev + num);
    } else {
      setRateInput(prev => prev + num);
      }
  }, [activeInput]);

  const handleBackspacePress = useCallback(() => {
    if (activeInput === ActiveInput.QUANTITY) {
      setQuantityInput(prev => prev.slice(0, -1));
    } else {
      setRateInput(prev => prev.slice(0, -1));
      }
  }, [activeInput]);

  const handleDecimalPress = useCallback(() => {
    if (activeInput === ActiveInput.QUANTITY) {
      if (!quantityInput.includes('.')) {
        setQuantityInput(prev => prev + '.');
      }
    } else if (activeInput === ActiveInput.RATE) {
      if (!rateInput.includes('.')) {
        setRateInput(prev => prev + '.');
      }
    }
  }, [activeInput, quantityInput, rateInput]);

  const handleClearPress = useCallback(() => {
    if (activeInput === ActiveInput.QUANTITY) {
      setQuantityInput('');
    } else {
      setRateInput('');
    }
  }, [activeInput]);

  const handleEnterPress = useCallback(() => {
    if (activeInput === ActiveInput.QUANTITY) {
      if (quantityInput) {
        setActiveInput(ActiveInput.RATE);
      }
    } else if (activeInput === ActiveInput.RATE) {
      if (rateInput && quantityInput) {
        try {
          // Validate quantity with comprehensive validation
          const quantityValidation = validateNumericInput(quantityInput, {
            min: 0.001,
            max: 999999,
            allowZero: false,
            maxDecimalPlaces: 3
          });

          if (!quantityValidation.isValid) {
            Alert.alert('Invalid Quantity', quantityValidation.error || 'Please enter a valid quantity.');
            return;
          }

          // Validate price with comprehensive validation
          const priceValidation = validateNumericInput(rateInput, {
            min: 0.01,
            max: 999999,
            allowZero: false,
            maxDecimalPlaces: 2
          });

          if (!priceValidation.isValid) {
            Alert.alert('Invalid Price', priceValidation.error || 'Please enter a valid price.');
            return;
          }

          // Create validated item with precise decimal calculation
          const newItem: BillItem = {
            name: showItemNameInput ? 'Item Name' : `Item ${items.length + 1}`,
            quantity: quantityValidation.value!,
            price: priceValidation.value!,
            amount: calculateItemAmount(quantityValidation.value!, priceValidation.value!)
          };

          setItems(prev => [...prev, newItem]);
          setQuantityInput('');
          setRateInput('');
          setActiveInput(ActiveInput.QUANTITY);
        } catch (error) {
          if (__DEV__) {
            console.error('[NewBill] Error adding item via enter:', error);
          }
          Alert.alert('Error', 'Failed to add item. Please check your input and try again.');
        }
      } else if (!quantityInput) {
        setActiveInput(ActiveInput.QUANTITY);
      }
    } else if (activeInput === ActiveInput.DISCOUNT) {
      handleApplyDiscount();
    }
  }, [activeInput, quantityInput, rateInput, items, showItemNameInput, setItems, setQuantityInput, setRateInput, setActiveInput]);

  const handleRemoveItem = useCallback((index: number) => {
    setItems(prevItems => prevItems.filter((_, i) => i !== index));
  }, []);

  const totals = useMemo(() => {
    try {
      // Calculate discount amount with proper validation and precision
      let calculatedDiscountAmount = 0;
      const discountVal = parseFloat(discountValue) || 0;

      if (discountVal > 0) {
        if (discountType === 'percentage') {
          // Use precise percentage calculation
          const billTotals = calculateBillTotals(items, 0); // Calculate subtotal first
          calculatedDiscountAmount = calculatePercentageDiscount(billTotals.subtotal, discountVal);
        } else {
          calculatedDiscountAmount = discountVal;
        }
      }

      // Use precise decimal calculation for final totals
      const finalTotals = calculateBillTotals(items, calculatedDiscountAmount);

      return {
        subTotal: finalTotals.subtotal,
        calculatedDiscountAmount: finalTotals.discount,
        finalTotal: finalTotals.finalTotal
      };
    } catch (error) {
      if (__DEV__) {
        console.error('[NewBill] Error calculating totals:', error);
      }
      // Fallback to safe values
      return { subTotal: 0, calculatedDiscountAmount: 0, finalTotal: 0 };
    }
  }, [items, discountType, discountValue]);

  const createBillObject = useCallback(() => {
    if (items.length === 0 && parseFloat(discountValue || '0') <= 0) {
        Alert.alert('Empty Bill', 'Cannot process an empty bill. Please add items or a discount.');
        return null;
    }

    const { subTotal, calculatedDiscountAmount, finalTotal } = totals;

    const newBill: Bill = {
      id: generateUUID(),
      date: new Date().toISOString(),
      items: [...items],
      totalAmount: subTotal,
      discountAmount: calculatedDiscountAmount,
      finalAmount: finalTotal,
      name: billName.trim() || `Bill #${Date.now().toString().slice(-4)}`,
    };

    return newBill;
  }, [items, totals, billName, discountValue]);

  const handleSavePress = useCallback(async () => {
    const bill = createBillObject();
    if (!bill) return;

    setIsProcessingAction(true);
    try {
      await saveBill(bill);
      Alert.alert('Bill Saved', 'Bill has been saved successfully to history.');
      setItems([]);
      setQuantityInput('');
      setRateInput('');
      setDiscountValue('');
      setAppliedDiscount(0);
      setDiscountType('percentage');
    } catch (error) {
      if (__DEV__) {
        console.error('Error saving bill:', error);
      }
      Alert.alert('Error', 'Failed to save the bill. Please try again.');
    } finally {
      setIsProcessingAction(false);
    }
  }, [createBillObject]);

  const handlePrintPress = useCallback(async () => {
    const bill = createBillObject();
    if (!bill) return;

    setIsProcessingAction(true);
    try {
      // Use printBill directly without checking for printer configuration
      await printBill(bill);
      Alert.alert('Bill Printed', 'The bill has been sent to the printer.');
      setItems([]);
      setQuantityInput('');
      setRateInput('');
      setDiscountValue('');
      setAppliedDiscount(0);
      setDiscountType('percentage');
    } catch (error) {
      if (__DEV__) {
        console.error('Error printing bill:', error);
      }
      const errorMessage = error instanceof Error ? error.message : 'Failed to print bill. Please try again.';
      Alert.alert('Error', errorMessage);
    } finally {
      setIsProcessingAction(false);
    }
  }, [createBillObject, router, setItems, setQuantityInput, setRateInput, setDiscountValue, setAppliedDiscount, setDiscountType, setIsProcessingAction]);

  const openDrawer = useCallback(() => {
    (navigation as any).openDrawer();
  }, [navigation]);

  const handleOpenDiscountDialog = useCallback(() => setIsDiscountDialogVisible(true), []);
  const handleCloseDiscountDialog = useCallback(() => setIsDiscountDialogVisible(false), []);

  const handleApplyDiscount = useCallback(() => {
    try {
      // Validate discount input
      const discountValidation = validateDiscountInput(discountValue, discountType, totals.subTotal);

      if (!discountValidation.isValid) {
        Alert.alert('Invalid Discount', discountValidation.error || 'Please enter a valid discount value.');
        return;
      }

      const { calculatedDiscountAmount } = totals;
      setAppliedDiscount(calculatedDiscountAmount);
      handleCloseDiscountDialog();
    } catch (error) {
      if (__DEV__) {
        console.error('[NewBill] Error applying discount:', error);
      }
      Alert.alert('Error', 'Failed to apply discount. Please try again.');
    }
  }, [totals, handleCloseDiscountDialog, discountValue, discountType]);

  const setActiveInputHandler = useCallback((inputType: ActiveInput) => {
    setActiveInput(inputType);
    // Clear discount input when switching away from it
    if (inputType !== ActiveInput.DISCOUNT) {
      setDiscountValue('');
    }
  }, []);

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: '#1a1a1a',
      paddingTop: Platform.OS === 'android' ? Constants.statusBarHeight : 0,
    },
    container: {
      flex: 1,
      backgroundColor: '#1a1a1a',
    },
    header: {
      backgroundColor: '#1a1a1a',
      paddingVertical: 12,
      paddingHorizontal: 16,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: '#ffffff',
      marginLeft: 10,
    },
    headerButtons: {
      flexDirection: 'row',
    },
    headerButton: {
      paddingVertical: 10,
      paddingHorizontal: 16,
      borderRadius: 8,
      marginLeft: 8,
    },
    headerButtonText: {
      fontSize: 14,
      fontWeight: 'bold',
      color: '#ffffff',
    },
    tableContainer: {
      flex: 1,
    },
    inputContainer: {
      backgroundColor: '#1a1a1a',
    },
    inputFields: {
      flexDirection: 'row',
      paddingHorizontal: 8,
      paddingVertical: 12,
      backgroundColor: '#262626',
      alignItems: 'center',
    },
    inputFieldWrapper: {
      flex: 1,
      marginRight: 8,
    },
    actionIconsContainer: {
      flexDirection: 'row',
    },
    actionIcon: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: '#444444',
      justifyContent: 'center',
      alignItems: 'center',
      marginLeft: 8,
    },
    loadingOverlay: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: 'rgba(0,0,0,0.7)',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 10,
    },
    loadingText: {
      marginTop: 16,
      color: '#fff',
      fontSize: 16,
    },
    inputSection: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      marginBottom: 10,
      backgroundColor: theme.colors.surfaceVariant,
      paddingVertical: 8,
      borderRadius: 8,
    },
    inputFieldContainer: {
      flex: 1,
      marginRight: 8,
    },
    rateInputFieldContainer: {
      flex: 1,
      marginRight: 8,
    },
    iconButtonsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    // Modern Discount Dialog Styles
    modernDiscountDialog: {
      backgroundColor: theme.colors.surface,
      borderRadius: 24,
      padding: 0,
      marginHorizontal: 20,
      marginVertical: 40,
      elevation: 8,
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 12,
    },
    discountDialogHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 24,
      paddingVertical: 20,
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
    },
    discountDialogIconContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 16,
    },
    discountDialogHeaderText: {
      flex: 1,
    },
    discountDialogTitle: {
      fontSize: 20,
      fontWeight: '700',
      marginBottom: 2,
    },
    discountDialogSubtitle: {
      fontSize: 14,
      fontWeight: '500',
      opacity: 0.8,
    },
    discountDialogCloseButton: {
      margin: 0,
    },
    discountDialogContent: {
      paddingHorizontal: 24,
      paddingVertical: 20,
    },
    discountTypeSection: {
      marginBottom: 20,
    },
    discountTypeLabel: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 12,
    },
    radioButtonContainer: {
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 12,
      padding: 8,
    },
    radioButtonRow: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
      paddingHorizontal: 8,
    },
    radioButtonText: {
      fontSize: 16,
      fontWeight: '500',
      marginLeft: 8,
      flex: 1,
    },
    discountInputSection: {
      marginBottom: 8,
    },
    discountInput: {
      backgroundColor: 'transparent',
    },
    discountDialogActions: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      paddingHorizontal: 24,
      paddingVertical: 16,
      gap: 12,
    },
    discountActionButton: {
      minWidth: 100,
    },
  });

  const { subTotal, finalTotal } = totals;

  if (isLoadingSetting) {
    return (
      <SafeAreaView style={{flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: theme.colors.background}}>
        <ActivityIndicator animating={true} size="large" color={theme.colors.primary} />
        <Text style={{marginTop: 16, color: theme.colors.onSurfaceVariant}}>Loading preferences...</Text>
      </SafeAreaView>
    );
  }

    return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar style="light" />

      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={openDrawer} style={styles.titleContainer}>
            <MaterialCommunityIcons name="menu" size={28} color="#ffffff" />
            <Text style={styles.headerTitle}>Quick Bill</Text>
          </TouchableOpacity>
        </View>

        {/* Bill Table */}
        <View style={styles.tableContainer}>
          <BillTable
            items={items}
            showItemNameColumn={showItemNameInput}
            subTotal={subTotal}
            discountAmount={appliedDiscount}
            finalAmount={finalTotal}
            onRemoveItem={handleRemoveItem}
          />
        </View>

        {/* Input Fields */}
        <View style={styles.inputContainer}>
          <View style={styles.inputSection}>
            <View style={styles.inputFieldContainer}>
              <BillInputField
                label="Quantity"
                value={quantityInput}
                isActive={activeInput === ActiveInput.QUANTITY}
                onPress={() => setActiveInputHandler(ActiveInput.QUANTITY)}
              />
            </View>
            <View style={styles.rateInputFieldContainer}>
              <BillInputField
                label="Rate"
                value={rateInput}
                isActive={activeInput === ActiveInput.RATE}
                onPress={() => setActiveInputHandler(ActiveInput.RATE)}
                icon="cash-multiple"
              />
            </View>
            <View style={styles.iconButtonsContainer}>
              <IconButton
                icon="printer"
                size={28}
                iconColor={theme.colors.primary}
                onPress={handlePrintPress}
                style={{ backgroundColor: theme.colors.primaryContainer, borderRadius: 12 }}
              />
              <IconButton
                icon="brightness-percent"
                size={28}
                iconColor={theme.colors.primary}
                onPress={handleOpenDiscountDialog}
                style={{ backgroundColor: theme.colors.primaryContainer, borderRadius: 12, marginLeft: 8 }}
              />
            </View>
          </View>

          {/* Number Pad */}
          <NumberPad
            onNumberPress={handleNumberPress}
            onBackspacePress={handleBackspacePress}
            onDecimalPress={handleDecimalPress}
            onClearPress={handleClearPress}
            onEnterPress={handleEnterPress}
            onSavePress={handleSavePress}
          />
        </View>

        <Portal>
          <Dialog
            visible={isDiscountDialogVisible}
            onDismiss={handleCloseDiscountDialog}
            style={styles.modernDiscountDialog}
          >
            {/* Modern Dialog Header */}
            <View style={[styles.discountDialogHeader, { backgroundColor: theme.colors.primaryContainer }]}>
              <View style={[styles.discountDialogIconContainer, { backgroundColor: theme.colors.primary }]}>
                <MaterialCommunityIcons
                  name="percent"
                  size={24}
                  color={theme.colors.onPrimary}
                />
              </View>
              <View style={styles.discountDialogHeaderText}>
                <Text style={[styles.discountDialogTitle, { color: theme.colors.onPrimaryContainer }]}>
                  Apply Discount
                </Text>
                <Text style={[styles.discountDialogSubtitle, { color: theme.colors.onPrimaryContainer }]}>
                  Configure discount for this bill
                </Text>
              </View>
              <IconButton
                icon="close"
                size={20}
                onPress={handleCloseDiscountDialog}
                style={styles.discountDialogCloseButton}
                iconColor={theme.colors.onPrimaryContainer}
              />
            </View>

            {/* Dialog Content */}
            <View style={styles.discountDialogContent}>
              <View style={styles.discountTypeSection}>
                <Text style={[styles.discountTypeLabel, { color: theme.colors.onSurface }]}>
                  Discount Type
                </Text>
                <RadioButton.Group
                  onValueChange={newValue => setDiscountType(newValue as 'percentage' | 'absolute')}
                  value={discountType}
                >
                  <View style={styles.radioButtonContainer}>
                    <View style={styles.radioButtonRow}>
                      <RadioButton value="percentage" color={theme.colors.primary}/>
                      <Text
                        onPress={() => setDiscountType('percentage')}
                        style={[styles.radioButtonText, { color: theme.colors.onSurface }]}
                      >
                        Percentage (%)
                      </Text>
                    </View>
                    <View style={styles.radioButtonRow}>
                      <RadioButton value="absolute" color={theme.colors.primary}/>
                      <Text
                        onPress={() => setDiscountType('absolute')}
                        style={[styles.radioButtonText, { color: theme.colors.onSurface }]}
                      >
                        Absolute Amount
                      </Text>
                    </View>
                  </View>
                </RadioButton.Group>
              </View>

              <View style={styles.discountInputSection}>
                <TextInput
                  label={`Discount Value ${discountType === 'percentage' ? '(%)' : '(₹)'}`}
                  value={discountValue}
                  onChangeText={setDiscountValue}
                  keyboardType="numeric"
                  mode="outlined"
                  style={styles.discountInput}
                  outlineColor={theme.colors.outline}
                  activeOutlineColor={theme.colors.primary}
                  textColor={theme.colors.onSurface}
                  placeholderTextColor={theme.colors.onSurfaceVariant}
                  left={discountType === 'absolute' ? <TextInput.Affix text="₹" /> : undefined}
                  right={discountType === 'percentage' ? <TextInput.Affix text="%" /> : undefined}
                />
              </View>
            </View>

            {/* Dialog Actions */}
            <View style={styles.discountDialogActions}>
              <Button
                mode="text"
                onPress={handleCloseDiscountDialog}
                textColor={theme.colors.onSurfaceVariant}
                style={styles.discountActionButton}
              >
                Cancel
              </Button>
              <Button
                mode="contained"
                onPress={handleApplyDiscount}
                buttonColor={theme.colors.primary}
                textColor={theme.colors.onPrimary}
                style={styles.discountActionButton}
              >
                Apply Discount
              </Button>
            </View>
          </Dialog>
        </Portal>

        {isProcessingAction && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>Processing...</Text>
          </View>
        )}
        </View>
    </SafeAreaView>
  );
}