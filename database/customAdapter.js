import SQLiteAdapter from '@nozbe/watermelondb/adapters/sqlite'
import LokiJSAdapter from '@nozbe/watermelondb/adapters/lokijs'
import { NativeModules, Platform } from 'react-native'

/**
 * Creates the appropriate database adapter based on the current environment
 * - In production: Uses SQLite with JSI for maximum performance
 * - During remote debugging: Falls back to LokiJS to avoid JSI issues
 *
 * @param {Object} options - Options for the adapter
 * @returns {SQLiteAdapter|LokiJSAdapter} The appropriate adapter instance
 */
export function createAdapter(options) {
  // More reliable debug detection
  const isDebugging = isDebugModeEnabled()

  // Display info about the adapter mode (only in development)
  if (__DEV__) {
    console.log(`[Database] Mode: ${isDebugging ? 'DEVELOPMENT WITH DEBUG' : 'PRODUCTION'} - ${isDebugging ? 'Using LokiJS' : 'Using SQLite with JSI'}`)
  }

  if (isDebugging) {
    if (__DEV__) {
      console.log('[Database] Remote debugging detected - Using LokiJS adapter for compatibility')
      console.log('[Database] NOTE: LokiJS is slower than SQLite with JSI')
      console.log('[Database] For full performance, disable remote debugging')
    }

    // Use LokiJS adapter for debugging, which works with remote debugging
    return new LokiJSAdapter({
      schema: options.schema,
      migrations: options.migrations,
      useWebWorker: false,
      useIncrementalIndexedDB: true,
      // Performance optimizations for LokiJS
      autosave: true,
      autosaveInterval: 2000,
      separateCollections: true,
    })
  } else {
    if (__DEV__) {
      console.log('[Database] Production mode - Using SQLite with JSI enabled')
    }

    // Use SQLite with JSI for maximum performance
    return new SQLiteAdapter({
      ...options,
      jsi: true,
      // Important: Set database location for Android, iOS handles this automatically
      dbName: Platform.OS === 'android' ? 'billingApp.db' : 'billingApp',
      // Performance optimizations
      synchronous: 'NORMAL', // Faster than FULL, safer than OFF
      cacheSize: 2000, // Increase cache size for better performance
      busyTimeout: 1000, // Reduce busy timeout for faster operations
      onSetUpError: error => {
        // Always log errors, even in production, but with less detail
        if (__DEV__) {
          console.error('[Database] SQLite setup error:', error)
        } else {
          console.error('[Database] SQLite setup failed')
        }

        if (options.onSetUpError) {
          options.onSetUpError(error)
        }
      }
    })
  }
}

/**
 * Detects if the app is running in debug mode with remote JS debugging
 *
 * @returns {boolean} True if remote debugging is enabled
 */
function isDebugModeEnabled() {
  if (!__DEV__) {
    return false
  }

  // Android specific check
  if (Platform.OS === 'android') {
    const isRemoteDebuggingEnabled = Boolean(
      NativeModules.DevSettings?.isRemoteDebuggingEnabled ||
      NativeModules.DevMenu?.isRemoteDebuggingEnabled
    )

    return isRemoteDebuggingEnabled
  }

  // iOS debugging check - approximate since there's no direct API
  // Check if setTimeout behaves like Chrome's
  try {
    // Original setTimeout will throw when accessing toString in JSC
    // but won't throw in Chrome's JS engine
    const originalSetTimeout = global.setTimeout
    const isChrome = !originalSetTimeout.toString().includes('[native code]')

    return isChrome
  } catch (e) {
    // If it throws, we're likely in JSC (native) so not debugging remotely
    return false
  }
}

/**
 * Gets information about the current database adapter
 * Useful for debugging and performance monitoring
 *
 * @param {SQLiteAdapter|LokiJSAdapter} adapter - The database adapter
 * @returns {Object} Information about the adapter
 */
export function getAdapterInfo(adapter) {
  const isLokiAdapter = adapter instanceof LokiJSAdapter
  const isSQLiteAdapter = adapter instanceof SQLiteAdapter

  return {
    type: isLokiAdapter ? 'LokiJS' : isSQLiteAdapter ? 'SQLite' : 'Unknown',
    jsiEnabled: isSQLiteAdapter ? adapter.jsi : false,
    debugMode: isDebugModeEnabled(),
    platform: Platform.OS,
  }
}