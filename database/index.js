import { Database } from '@nozbe/watermelondb'
import { synchronize } from '@nozbe/watermelondb/sync'
import { createAdapter, getAdapterInfo } from './customAdapter'

import schema from './schemas'
import migrations from './migrations'
import { Bill, Customer, Item, Payment } from './models'

// Initialize the database adapter using our custom adapter factory
const adapter = createAdapter({
  schema,
  migrations,
  // The custom adapter will handle JSI settings based on debug mode
  onSetUpError: error => {
    if (__DEV__) {
      console.error('Database setup error:', error)
    } else {
      console.error('Database setup failed')
    }
  }
})

// Create the database instance
export const database = new Database({
  adapter,
  modelClasses: [
    Bill,
    Customer,
    Item,
    Payment
  ],
  // Enable actions for sync capability
  actionsEnabled: true
})

// Export models for easy access
export const billsCollection = database.get('bills')
export const customersCollection = database.get('customers')
export const itemsCollection = database.get('items')
export const paymentsCollection = database.get('payments')

// Export adapter info for performance monitoring
export const adapterInfo = getAdapterInfo(adapter)

// Log information about the adapter for debugging (development only)
if (__DEV__) {
  console.log('[Database] Adapter Info:', adapterInfo)
}

/**
 * Reset the database (for development/testing only)
 * WARNING: This will delete all data!
 */
export const resetDatabase = async () => {
  if (!__DEV__) {
    console.warn('[Database] Attempted to reset database in production mode. Ignoring.')
    return
  }

  await database.write(async () => {
    await database.unsafeResetDatabase()
  })

  if (__DEV__) {
    console.log('[Database] Database has been reset successfully')
  }
}

/**
 * Sync the database with a remote server
 * This is optional and can be used if you need cloud sync
 *
 * @param {string} serverUrl - The URL of the sync server
 * @param {string} authToken - Authentication token
 * @returns {Promise<boolean>} Success or failure
 */
export const syncDatabase = async (serverUrl, authToken) => {
  try {
    // Validate inputs
    if (!serverUrl || typeof serverUrl !== 'string') {
      throw new Error('Invalid server URL provided');
    }

    if (!authToken || typeof authToken !== 'string') {
      throw new Error('Invalid authentication token provided');
    }

    // Validate URL format and protocol
    let validatedUrl;
    try {
      validatedUrl = new URL(serverUrl);
      if (!['http:', 'https:'].includes(validatedUrl.protocol)) {
        throw new Error('Only HTTP and HTTPS protocols are allowed');
      }
    } catch (urlError) {
      throw new Error(`Invalid URL format: ${urlError.message}`);
    }
    await synchronize({
      database,
      pullChanges: async ({ lastPulledAt }) => {
        // Use validated URL for security
        const pullUrl = new URL('/sync', validatedUrl);
        pullUrl.searchParams.set('lastPulledAt', lastPulledAt || '');

        const response = await fetch(pullUrl.toString(), {
          headers: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        })

        if (!response.ok) {
          throw new Error(`Pull changes failed with status: ${response.status}`)
        }

        const { changes, timestamp } = await response.json()
        return { changes, timestamp }
      },
      pushChanges: async ({ changes, lastPulledAt }) => {
        // Use validated URL for security
        const pushUrl = new URL('/sync', validatedUrl);

        const response = await fetch(pushUrl.toString(), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${authToken}`
          },
          body: JSON.stringify({ changes, lastPulledAt })
        })

        if (!response.ok) {
          throw new Error(`Push changes failed with status: ${response.status}`)
        }
      },
      migrationsEnabledAtVersion: 1
    })

    if (__DEV__) {
      console.log('[Database] Sync completed successfully')
    }
    return true
  } catch (error) {
    if (__DEV__) {
      console.error('[Database] Sync failed:', error)
    } else {
      console.error('[Database] Sync failed')
    }
    return false
  }
}

/**
 * Get database metrics for performance monitoring
 *
 * @returns {Promise<Object>} Database metrics
 */
export const getDatabaseMetrics = async () => {
  // Get counts for each collection
  const [billsCount, customersCount, itemsCount, paymentsCount] = await Promise.all([
    billsCollection.query().fetchCount(),
    customersCollection.query().fetchCount(),
    itemsCollection.query().fetchCount(),
    paymentsCollection.query().fetchCount()
  ])

  return {
    adapter: adapterInfo,
    counts: {
      bills: billsCount,
      customers: customersCount,
      items: itemsCount,
      payments: paymentsCount,
      total: billsCount + customersCount + itemsCount + paymentsCount
    }
  }
}