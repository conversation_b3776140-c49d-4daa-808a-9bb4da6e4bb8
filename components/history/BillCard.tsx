import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Card, IconButton, Surface, Text, useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { Bill } from '@/types/bill';

interface BillCardProps {
  bill: Bill;
  onViewDetails: (bill: Bill) => void;
  onShare: (bill: Bill) => void;
  onEdit: (bill: Bill) => void;
  onPrint: (bill: Bill) => void;
  onDelete: (billId: string) => void;
}

const BillCard: React.FC<BillCardProps> = ({
  bill,
  onViewDetails,
  onShare,
  onEdit,
  onPrint,
  onDelete
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [isSharing, setIsSharing] = React.useState(false);

  // Format date in a more readable way
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  // Handle share with loading state
  const handleShare = async () => {
    setIsSharing(true);
    try {
      await onShare(bill);
    } finally {
      setIsSharing(false);
    }
  };

  return (
    <Surface style={styles.surface} elevation={1}>
      <Card
        style={styles.card}
        onPress={() => onViewDetails(bill)}
        mode="elevated"
        elevation={0}
      >
        <View style={styles.cardContent}>
          {/* Bill Info */}
          <View style={styles.mainContent}>
            <View style={styles.nameContainer}>
              <Text style={styles.billName}>
                {bill.name || `Bill #${bill.id.slice(-8)}`}
              </Text>
              <Text style={styles.billDate}>
                {formatDate(bill.date)}
              </Text>
            </View>

            <View style={styles.amountContainer}>
              <Text style={styles.billAmount}>
                ₹{(bill.finalAmount ?? bill.totalAmount).toFixed(2)}
              </Text>
              <Text style={styles.itemsCount}>
                {bill.items.length} {bill.items.length === 1 ? 'item' : 'items'}
              </Text>
            </View>
          </View>

          {/* Actions */}
          <View style={styles.actionsRow}>
            <IconButton
              icon={isSharing ? "loading" : "share-variant-outline"}
              size={20}
              style={styles.iconButton}
              onPress={handleShare}
              mode="outlined"
              disabled={isSharing}
            />
            <IconButton
              icon="pencil-outline"
              size={20}
              style={styles.iconButton}
              onPress={() => onEdit(bill)}
              mode="outlined"
            />
            <IconButton
              icon="printer-outline"
              size={20}
              style={styles.iconButton}
              onPress={() => onPrint(bill)}
              mode="outlined"
            />
            <IconButton
              icon="trash-can-outline"
              size={20}
              style={[styles.iconButton, styles.deleteButton]}
              iconColor={theme.colors.error}
              onPress={() => onDelete(bill.id)}
              mode="outlined"
            />
          </View>
        </View>
      </Card>
    </Surface>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  surface: {
    marginBottom: 16,
    borderRadius: 16,
    backgroundColor: theme.colors.surface,
  },
  card: {
    borderRadius: 16,
    backgroundColor: theme.colors.surface,
  },
  cardContent: {
    padding: 16,
  },
  mainContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  nameContainer: {
    flex: 2,
    justifyContent: 'center',
  },
  billName: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.onSurface,
    marginBottom: 4,
  },
  billDate: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
  },
  amountContainer: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  billAmount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: 4,
  },
  itemsCount: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
  },
  actionsRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  iconButton: {
    margin: 0,
    marginLeft: 8,
    backgroundColor: theme.colors.surfaceVariant,
  },
  deleteButton: {
    borderColor: theme.colors.errorContainer,
  }
});

export default BillCard;