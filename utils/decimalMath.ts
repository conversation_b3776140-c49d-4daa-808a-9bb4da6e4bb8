/**
 * Decimal arithmetic utilities for financial calculations
 * Prevents floating-point precision errors in currency calculations
 */

// Number of decimal places for currency calculations
const CURRENCY_DECIMAL_PLACES = 2;
const QUANTITY_DECIMAL_PLACES = 3;

/**
 * Convert a number to integer representation for precise calculations
 * @param value The decimal value
 * @param decimalPlaces Number of decimal places to preserve
 * @returns Integer representation
 */
function toInteger(value: number, decimalPlaces: number = CURRENCY_DECIMAL_PLACES): number {
  return Math.round(value * Math.pow(10, decimalPlaces));
}

/**
 * Convert integer representation back to decimal
 * @param intValue Integer representation
 * @param decimalPlaces Number of decimal places
 * @returns Decimal value
 */
function fromInteger(intValue: number, decimalPlaces: number = CURRENCY_DECIMAL_PLACES): number {
  return intValue / Math.pow(10, decimalPlaces);
}

/**
 * Add two decimal numbers with precision
 * @param a First number
 * @param b Second number
 * @param decimalPlaces Number of decimal places (default: 2 for currency)
 * @returns Sum with proper precision
 */
export function decimalAdd(a: number, b: number, decimalPlaces: number = CURRENCY_DECIMAL_PLACES): number {
  const intA = toInteger(a, decimalPlaces);
  const intB = toInteger(b, decimalPlaces);
  return fromInteger(intA + intB, decimalPlaces);
}

/**
 * Subtract two decimal numbers with precision
 * @param a First number (minuend)
 * @param b Second number (subtrahend)
 * @param decimalPlaces Number of decimal places (default: 2 for currency)
 * @returns Difference with proper precision
 */
export function decimalSubtract(a: number, b: number, decimalPlaces: number = CURRENCY_DECIMAL_PLACES): number {
  const intA = toInteger(a, decimalPlaces);
  const intB = toInteger(b, decimalPlaces);
  return fromInteger(intA - intB, decimalPlaces);
}

/**
 * Multiply two decimal numbers with precision
 * @param a First number
 * @param b Second number
 * @param resultDecimalPlaces Number of decimal places for result (default: 2 for currency)
 * @returns Product with proper precision
 */
export function decimalMultiply(
  a: number, 
  b: number, 
  resultDecimalPlaces: number = CURRENCY_DECIMAL_PLACES
): number {
  // For multiplication, we need to handle the decimal places carefully
  // Convert both numbers to integers, multiply, then adjust for decimal places
  const aDecimalPlaces = getDecimalPlaces(a);
  const bDecimalPlaces = getDecimalPlaces(b);
  
  const intA = toInteger(a, aDecimalPlaces);
  const intB = toInteger(b, bDecimalPlaces);
  
  // Multiply and adjust for the combined decimal places
  const product = intA * intB;
  const totalDecimalPlaces = aDecimalPlaces + bDecimalPlaces;
  
  // Convert back to decimal and round to desired precision
  const result = product / Math.pow(10, totalDecimalPlaces);
  return roundToDecimalPlaces(result, resultDecimalPlaces);
}

/**
 * Divide two decimal numbers with precision
 * @param a Dividend
 * @param b Divisor
 * @param decimalPlaces Number of decimal places (default: 2 for currency)
 * @returns Quotient with proper precision
 */
export function decimalDivide(a: number, b: number, decimalPlaces: number = CURRENCY_DECIMAL_PLACES): number {
  if (b === 0) {
    throw new Error('Division by zero');
  }
  
  // For division, we multiply the dividend by a power of 10 to maintain precision
  const multiplier = Math.pow(10, decimalPlaces);
  const intA = toInteger(a, decimalPlaces) * multiplier;
  const intB = toInteger(b, decimalPlaces);
  
  const quotient = Math.round(intA / intB);
  return fromInteger(quotient, decimalPlaces);
}

/**
 * Round a number to specified decimal places
 * @param value Number to round
 * @param decimalPlaces Number of decimal places
 * @returns Rounded number
 */
export function roundToDecimalPlaces(value: number, decimalPlaces: number): number {
  const multiplier = Math.pow(10, decimalPlaces);
  return Math.round(value * multiplier) / multiplier;
}

/**
 * Get the number of decimal places in a number
 * @param value The number to check
 * @returns Number of decimal places
 */
function getDecimalPlaces(value: number): number {
  const str = value.toString();
  const decimalIndex = str.indexOf('.');
  return decimalIndex === -1 ? 0 : str.length - decimalIndex - 1;
}

/**
 * Calculate bill item amount (quantity × price) with precision
 * @param quantity Item quantity
 * @param price Item price
 * @returns Amount with proper currency precision
 */
export function calculateItemAmount(quantity: number, price: number): number {
  return decimalMultiply(quantity, price, CURRENCY_DECIMAL_PLACES);
}

/**
 * Calculate percentage discount amount
 * @param subtotal Subtotal amount
 * @param percentage Discount percentage (0-100)
 * @returns Discount amount with proper precision
 */
export function calculatePercentageDiscount(subtotal: number, percentage: number): number {
  const discountDecimal = decimalDivide(percentage, 100, 4); // Use 4 decimal places for percentage calculation
  return decimalMultiply(subtotal, discountDecimal, CURRENCY_DECIMAL_PLACES);
}

/**
 * Calculate bill totals with precision
 * @param items Array of bill items
 * @param discountAmount Discount amount
 * @returns Object with subtotal, discount, and final total
 */
export function calculateBillTotals(
  items: Array<{ quantity: number; price: number; amount?: number }>,
  discountAmount: number = 0
): { subtotal: number; discount: number; finalTotal: number } {
  // Calculate subtotal by summing all item amounts
  let subtotal = 0;
  for (const item of items) {
    const itemAmount = item.amount ?? calculateItemAmount(item.quantity, item.price);
    subtotal = decimalAdd(subtotal, itemAmount);
  }
  
  // Ensure discount doesn't exceed subtotal
  const actualDiscount = Math.min(discountAmount, subtotal);
  
  // Calculate final total
  const finalTotal = decimalSubtract(subtotal, actualDiscount);
  
  return {
    subtotal: roundToDecimalPlaces(subtotal, CURRENCY_DECIMAL_PLACES),
    discount: roundToDecimalPlaces(actualDiscount, CURRENCY_DECIMAL_PLACES),
    finalTotal: Math.max(0, roundToDecimalPlaces(finalTotal, CURRENCY_DECIMAL_PLACES)) // Ensure non-negative
  };
}

/**
 * Format currency value for display
 * @param value Currency value
 * @param currencySymbol Currency symbol (default: ₹)
 * @param decimalPlaces Number of decimal places to show
 * @returns Formatted currency string
 */
export function formatCurrency(
  value: number, 
  currencySymbol: string = '₹', 
  decimalPlaces: number = CURRENCY_DECIMAL_PLACES
): string {
  const roundedValue = roundToDecimalPlaces(value, decimalPlaces);
  return `${currencySymbol}${roundedValue.toFixed(decimalPlaces)}`;
}

/**
 * Parse currency string to number
 * @param currencyString String representation of currency
 * @returns Parsed number value
 */
export function parseCurrency(currencyString: string): number {
  // Remove currency symbols and whitespace
  const cleanString = currencyString.replace(/[₹$€£¥,\s]/g, '');
  const value = parseFloat(cleanString);
  
  if (isNaN(value)) {
    throw new Error('Invalid currency format');
  }
  
  return roundToDecimalPlaces(value, CURRENCY_DECIMAL_PLACES);
}

/**
 * Check if two currency values are equal (accounting for floating point precision)
 * @param a First value
 * @param b Second value
 * @param tolerance Tolerance for comparison (default: 0.01)
 * @returns True if values are equal within tolerance
 */
export function currencyEquals(a: number, b: number, tolerance: number = 0.01): boolean {
  return Math.abs(a - b) < tolerance;
}

// Export constants for use in other modules
export const DECIMAL_PLACES = {
  CURRENCY: CURRENCY_DECIMAL_PLACES,
  QUANTITY: QUANTITY_DECIMAL_PLACES
} as const;
