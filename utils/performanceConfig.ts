/**
 * Performance Configuration for Billing App
 * Optimized for speed and minimal animations
 */

// Disable all animations globally
export const PERFORMANCE_CONFIG = {
  // Animation settings
  ANIMATIONS_ENABLED: false,
  TRANSITION_DURATION: 0,
  FADE_DURATION: 0,
  
  // Database settings
  DATABASE_BATCH_SIZE: 50,
  DATABASE_CACHE_SIZE: 100,
  
  // UI settings
  LIST_INITIAL_NUM_TO_RENDER: 10,
  LIST_MAX_TO_RENDER_PER_BATCH: 5,
  LIST_WINDOW_SIZE: 10,
  
  // Image settings
  IMAGE_CACHE_SIZE: 50,
  IMAGE_QUALITY: 0.8,
  
  // Network settings
  REQUEST_TIMEOUT: 5000,
  
  // File operations
  FILE_OPERATION_TIMEOUT: 3000,
  
  // Sharing settings
  PDF_QUALITY: 'fast', // 'fast' | 'normal' | 'high'
  PDF_COMPRESSION: true,
};

// React Native performance optimizations
export const REACT_NATIVE_CONFIG = {
  // FlatList optimizations
  getItemLayout: (data: any[], index: number) => ({
    length: 80, // Estimated item height
    offset: 80 * index,
    index,
  }),
  
  // Remove clipped subviews for better performance
  removeClippedSubviews: true,
  
  // Disable scroll indicator for better performance
  showsVerticalScrollIndicator: false,
  showsHorizontalScrollIndicator: false,
  
  // Optimize keyboard handling
  keyboardShouldPersistTaps: 'handled' as const,
  
  // Optimize touch handling
  delayPressIn: 0,
  delayPressOut: 0,
  delayLongPress: 200,
};

// Navigation optimizations
export const NAVIGATION_CONFIG = {
  // Disable animations
  animation: 'none' as const,
  animationDuration: 0,
  
  // Preload screens
  lazy: false,
  
  // Optimize transitions
  transitionSpec: {
    open: {
      animation: 'timing',
      config: { duration: 0 },
    },
    close: {
      animation: 'timing', 
      config: { duration: 0 },
    },
  },
};

// Database optimizations
export const DATABASE_CONFIG = {
  // Batch operations
  batchSize: 50,
  
  // Cache settings
  cacheSize: 100,
  
  // Query optimizations
  enableQueryCache: true,
  queryTimeout: 3000,
  
  // Connection settings
  maxConnections: 1,
  busyTimeout: 1000,
};

// Memory management
export const MEMORY_CONFIG = {
  // Clear cache intervals (in ms)
  imageCacheClearInterval: 300000, // 5 minutes
  componentCacheClearInterval: 600000, // 10 minutes
  
  // Memory thresholds
  lowMemoryThreshold: 0.8,
  criticalMemoryThreshold: 0.9,
  
  // Garbage collection hints
  enableGCHints: true,
  gcInterval: 30000, // 30 seconds
};

// Export all configs
export const PERFORMANCE_SETTINGS = {
  ...PERFORMANCE_CONFIG,
  reactNative: REACT_NATIVE_CONFIG,
  navigation: NAVIGATION_CONFIG,
  database: DATABASE_CONFIG,
  memory: MEMORY_CONFIG,
};
