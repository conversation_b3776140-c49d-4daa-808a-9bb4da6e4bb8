import * as SecureStore from 'expo-secure-store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// Encryption utility using built-in secure storage
class SecureStorageManager {
  private static instance: SecureStorageManager;
  private isSecureStoreAvailable: boolean = false;

  private constructor() {
    // Check if SecureStore is available (iOS Keychain, Android Keystore)
    this.isSecureStoreAvailable = Platform.OS === 'ios' || Platform.OS === 'android';
  }

  public static getInstance(): SecureStorageManager {
    if (!SecureStorageManager.instance) {
      SecureStorageManager.instance = new SecureStorageManager();
    }
    return SecureStorageManager.instance;
  }

  /**
   * Store sensitive data securely
   * @param key Storage key
   * @param value Data to store (will be JSON stringified)
   * @param options Storage options
   */
  async setSecureItem(key: string, value: any, options?: SecureStore.SecureStoreOptions): Promise<void> {
    try {
      const stringValue = typeof value === 'string' ? value : JSON.stringify(value);

      if (this.isSecureStoreAvailable) {
        // Use secure storage for sensitive data
        await SecureStore.setItemAsync(key, stringValue, {
          requireAuthentication: false, // Set to true if you want biometric/PIN protection
          ...options
        });
      } else {
        // Fallback to AsyncStorage with warning
        if (__DEV__) {
          console.warn(`[SecureStorage] SecureStore not available, falling back to AsyncStorage for key: ${key}`);
        }
        await AsyncStorage.setItem(key, stringValue);
      }
    } catch (error) {
      if (__DEV__) {
        console.error(`[SecureStorage] Error storing secure item for key ${key}:`, error);
      }
      throw new Error(`Failed to store secure item: ${error.message}`);
    }
  }

  /**
   * Retrieve sensitive data securely
   * @param key Storage key
   * @param options Retrieval options
   * @returns Parsed data or null if not found
   */
  async getSecureItem(key: string, options?: SecureStore.SecureStoreOptions): Promise<any> {
    try {
      let stringValue: string | null = null;

      if (this.isSecureStoreAvailable) {
        stringValue = await SecureStore.getItemAsync(key, options);
      } else {
        stringValue = await AsyncStorage.getItem(key);
      }

      if (stringValue === null) {
        return null;
      }

      // Try to parse as JSON, fallback to string if parsing fails
      try {
        return JSON.parse(stringValue);
      } catch {
        return stringValue;
      }
    } catch (error) {
      if (__DEV__) {
        console.error(`[SecureStorage] Error retrieving secure item for key ${key}:`, error);
      }
      return null;
    }
  }

  /**
   * Remove sensitive data
   * @param key Storage key
   * @param options Removal options
   */
  async removeSecureItem(key: string, options?: SecureStore.SecureStoreOptions): Promise<void> {
    try {
      if (this.isSecureStoreAvailable) {
        await SecureStore.deleteItemAsync(key, options);
      } else {
        await AsyncStorage.removeItem(key);
      }
    } catch (error) {
      if (__DEV__) {
        console.error(`[SecureStorage] Error removing secure item for key ${key}:`, error);
      }
      throw new Error(`Failed to remove secure item: ${error.message}`);
    }
  }

  /**
   * Check if secure storage is available
   */
  isSecureStorageAvailable(): boolean {
    return this.isSecureStoreAvailable;
  }

  /**
   * Clear all secure storage (use with caution)
   */
  async clearAllSecureItems(): Promise<void> {
    try {
      if (this.isSecureStoreAvailable) {
        // SecureStore doesn't have a clear all method, so we need to track keys
        if (__DEV__) {
          console.warn('[SecureStorage] SecureStore does not support clearing all items. Remove items individually.');
        }
      } else {
        await AsyncStorage.clear();
      }
    } catch (error) {
      if (__DEV__) {
        console.error('[SecureStorage] Error clearing secure storage:', error);
      }
      throw new Error(`Failed to clear secure storage: ${error.message}`);
    }
  }
}

// Export singleton instance
export const secureStorage = SecureStorageManager.getInstance();

// Storage keys for different types of sensitive data
export const SECURE_STORAGE_KEYS = {
  BILLS_DATA: 'secure_bills_data',
  CUSTOMER_DATA: 'secure_customer_data',
  FINANCIAL_SETTINGS: 'secure_financial_settings',
  PRINTER_CREDENTIALS: 'secure_printer_credentials',
  APP_SETTINGS: 'secure_app_settings'
} as const;

// Type for storage keys
export type SecureStorageKey = typeof SECURE_STORAGE_KEYS[keyof typeof SECURE_STORAGE_KEYS];
