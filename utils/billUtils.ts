import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import { Bill } from '../types/bill';
import * as FileSystem from 'expo-file-system';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Default currency symbol
export const DEFAULT_CURRENCY_SYMBOL = '₹';

// Function to get the current currency symbol from storage
export const getCurrencySymbol = async (): Promise<string> => {
  try {
    const storedCurrency = await AsyncStorage.getItem('gift_corner_currency');
    return storedCurrency || DEFAULT_CURRENCY_SYMBOL;
  } catch (error) {
    console.error('Error loading currency setting:', error);
    return DEFAULT_CURRENCY_SYMBOL; // Fallback to default
  }
};

// Function to format amount with user's currency preference
export const formatCurrency = async (amount: number): Promise<string> => {
  const symbol = await getCurrencySymbol();
  return `${symbol}${amount.toFixed(2)}`;
};

// Synchronous version for when async is not convenient
export const formatCurrencySync = (amount: number, symbol: string = DEFAULT_CURRENCY_SYMBOL): string => {
  return `${symbol}${amount.toFixed(2)}`;
};

// Function to generate HTML for printing
export const generateBillHTML = async (bill: Bill, outputType: 'thermal' | 'pdf' = 'thermal'): Promise<string> => {
  const dateTime = new Date(bill.date);
  const formattedDate = dateTime.toLocaleDateString('en-IN');
  const formattedTime = dateTime.toLocaleTimeString('en-IN');

  const billNumber = bill.id.substring(0, 8).toUpperCase();
  const currencySymbol = await getCurrencySymbol();
  const isPdfOutput = outputType === 'pdf';

  // When outputType is 'pdf' (for sharing), apply an artistic layout for A4 page
  const fontFamily = "'Courier New', monospace"; // Always Courier New as per request

  if (isPdfOutput) {
    // A4 artistic PDF layout
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
          <style>
            @page {
              size: A4;
              margin: 0;
            }
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
              font-family: ${fontFamily};
            }

            body {
              width: 210mm;
              height: 297mm;
              margin: 0 auto;
              padding: 10mm;
              background-color: white;
              color: black;
            }

            .invoice-container {
              border: 1px solid #ccc;
              border-radius: 5mm;
              position: relative;
              overflow: hidden;
              box-shadow: 0 0 10px rgba(0,0,0,0.1);
              display: flex;
              flex-direction: column;
              min-height: 277mm;
              page-break-inside: avoid;
              justify-content: space-between;
            }

            .header {
              text-align: center;
              padding: 8mm;
              background-color: #f8f8f8;
              border-bottom: 2px solid #eee;
            }

            .shop-name {
              font-size: 28px;
              font-weight: bold;
              color: #333;
              margin-bottom: 3mm;
            }

            .receipt-title {
              font-size: 22px;
              font-weight: bold;
              color: #555;
              margin-bottom: 5mm;
              text-transform: uppercase;
              letter-spacing: 2px;
            }

            .address-line {
              margin-bottom: 1mm;
              font-size: 14px;
              color: #666;
            }

            .info-section {
              display: flex;
              justify-content: space-between;
              padding: 8mm;
              background-color: #fbfbfb;
            }

            .info-left, .info-right {
              flex: 1;
            }

            .info-row {
              margin-bottom: 3mm;
              font-size: 16px;
            }

            .info-label {
              font-weight: bold;
              color: #555;
              margin-right: 2mm;
            }

            .bill-number {
              font-size: 18px;
              color: #333;
              font-weight: bold;
            }

            .table-container {
              flex: 1;
              padding: 0 8mm;
              margin-bottom: 8mm;
              display: flex;
              flex-direction: column;
            }

            table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 5mm;
              flex-grow: 1;
            }

            th {
              background-color: #f2f2f2;
              text-align: left;
              padding: 3mm;
              font-size: 16px;
              border-bottom: 1px solid #ddd;
              color: #444;
            }

            td {
              padding: 3.5mm;
              font-size: 15px;
              border-bottom: 1px solid #eee;
            }

            .qty-col {
              text-align: center;
              width: 15%;
            }

            .price-col {
              text-align: right;
              width: 20%;
            }

            .amount-col {
              text-align: right;
              width: 20%;
            }

            .summary-details {
              margin: 5mm;
              padding: 5mm;
              background-color: #f8f8f8;
              border-radius: 3mm;
              margin-top: auto;
            }

            .summary-row {
              display: flex;
              justify-content: space-between;
              margin-bottom: 2mm;
              font-size: 16px;
            }

            .grand-total {
              font-size: 20px;
              font-weight: bold;
              margin-top: 4mm;
              padding-top: 4mm;
              border-top: 1px solid #ddd;
              color: #333;
            }

            .barcode {
              font-family: monospace;
              letter-spacing: 2px;
              margin: 8mm 0;
              text-align: center;
              font-size: 16px;
            }

            .footer {
              text-align: center;
              padding: 8mm;
              background-color: #f8f8f8;
              border-top: 2px solid #eee;
              font-size: 14px;
              color: #666;
              margin-top: auto;
            }

            .footer-text {
              margin-bottom: 2mm;
            }

            .items-table {
              margin-bottom: 5mm;
            }

            .stamp {
              position: absolute;
              bottom: 80mm;
              right: 20mm;
              transform: rotate(-15deg);
              font-size: 32px;
              color: rgba(0,0,0,0.1);
              border: 4px solid rgba(0,0,0,0.1);
              border-radius: 8px;
              padding: 5mm;
              font-weight: bold;
            }
          </style>
        </head>
        <body>
          <div class="invoice-container">
            <div class="header">
              <div class="shop-name">GIFT CORNER</div>
              <div class="receipt-title">Invoice</div>
              <div class="address-line">Hindi Bazaar, Ghanta Ghar, Gorakhpur</div>
              <div class="address-line">Tel: 9119810269, 9956558465</div>
            </div>

            <div class="info-section">
              <div class="info-left">
                <div class="info-row">
                  <span class="info-label">Date:</span>
                  <span>${formattedDate}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">Time:</span>
                  <span>${formattedTime}</span>
                </div>
              </div>
              <div class="info-right">
                <div class="info-row">
                  <span class="info-label">Invoice #:</span>
                  <span class="bill-number">GC-${billNumber}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">Customer:</span>
                  <span>${bill.name || 'Customer'}</span>
                </div>
              </div>
            </div>

            <div class="table-container">
              <table class="items-table">
                <thead>
                  <tr>
                    <th>Item</th>
                    <th class="qty-col">Qty</th>
                    <th class="price-col">Price</th>
                    <th class="amount-col">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  ${bill.items.map((item, index) => `
                    <tr>
                      <td>${item.name || `Item ${index + 1}`}</td>
                      <td class="qty-col">${item.quantity}</td>
                      <td class="price-col">${formatCurrencySync(item.price, currencySymbol)}</td>
                      <td class="amount-col">${formatCurrencySync(item.amount, currencySymbol)}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>

              <div class="summary-details">
                <div class="summary-row">
                  <span>Subtotal:</span>
                  <span>${formatCurrencySync(bill.totalAmount, currencySymbol)}</span>
                </div>
                ${bill.discountAmount && bill.discountAmount > 0 ? `
                  <div class="summary-row">
                    <span>Discount:</span>
                    <span>- ${formatCurrencySync(bill.discountAmount, currencySymbol)}</span>
                  </div>
                ` : ''}
                <div class="summary-row grand-total">
                  <span>TOTAL:</span>
                  <span>${formatCurrencySync(bill.finalAmount ?? bill.totalAmount, currencySymbol)}</span>
                </div>
              </div>
            </div>

            <div class="barcode">
              *GC${billNumber}*
            </div>

            <div class="stamp">PAID</div>

            <div class="footer">
              <div class="footer-text">Thank you for shopping at GIFT CORNER!</div>
              <div class="footer-text">All items sold are non-returnable</div>
            </div>
          </div>
        </body>
      </html>
    `;
  } else {
    // Original thermal printer layout
    const bodyPadding = "0";
    const bodyFontSize = "12px";
    const bodyLineHeight = "1.2";
    const bodyWidth = '302px';

    const shopNameFontSize = "18px";
    const shopNameMarginBottom = "3px";
    const receiptTitleFontSize = "14px";
    const receiptTitleMarginBottom = "5px";
    const dividerStyle = "1px dashed black";
    const dividerMargin = "8px 0";
    const tableCellPadding = "3px 2px";
    const summaryDetailFontSize = "12px";
    const summaryGrandTotalFontSize = "14px";
    const barcodeMargin = "10px 0";
    const footerMarginTop = "10px";
    const footerMarginBottom = "30px";

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
          <style>
            /* Common styles */
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
              font-family: ${fontFamily};
            }

            body {
              width: ${bodyWidth};
              margin: 0 auto;
              padding: ${bodyPadding};
              background-color: white;
              color: black;
              font-size: ${bodyFontSize};
              line-height: ${bodyLineHeight};
            }

            .text-center {
              text-align: center;
            }

            .text-right {
              text-align: right;
            }

            .bold {
              font-weight: bold;
            }

            .header {
              text-align: center;
              padding: 5mm;
              background-color: #f8f8f8;
              border-bottom: 2px solid #eee;
            }

            .shop-name {
              font-size: ${shopNameFontSize};
              font-weight: bold;
              margin-bottom: ${shopNameMarginBottom};
            }

            .receipt-title {
              font-size: ${receiptTitleFontSize};
              font-weight: bold;
              margin-bottom: ${receiptTitleMarginBottom};
            }

            .address-line {
              margin-bottom: 2px;
            }

            .divider {
              border-bottom: ${dividerStyle};
              margin: ${dividerMargin};
              width: 100%;
            }

            .info-row {
              display: flex;
              justify-content: space-between;
              margin-bottom: 3px;
            }

            .info-label {
              font-weight: bold;
            }

            table {
              width: 100%;
              border-collapse: collapse;
              margin: 8px 0;
            }

            th, td {
              text-align: left;
              padding: ${tableCellPadding};
            }

            th {
              border-bottom: 1px solid black;
              font-weight: bold;
            }

            .qty-col {
              text-align: center;
              width: 40px;
            }

            .price-col {
              text-align: right;
              width: 60px;
            }

            .amount-col {
              text-align: right;
              width: 70px;
            }

            .summary-details {
              margin-top: 5px;
              padding-top: 5px;
              border-top: 1px solid black;
            }

            .summary-details .info-row {
              font-size: ${summaryDetailFontSize};
            }

            .summary-details .info-row.grand-total {
              font-size: ${summaryGrandTotalFontSize};
              font-weight: bold;
              margin-top: 3px;
            }

            .barcode {
              font-family: monospace;
              letter-spacing: 2px;
              margin: ${barcodeMargin};
            }

            .footer {
              margin-top: ${footerMarginTop};
              margin-bottom: ${footerMarginBottom};
            }

            .footer-text {
              margin-bottom: 3px;
            }
          </style>
        </head>
        <body>
          <div class="header text-center">
            <div class="shop-name">GIFT CORNER</div>
            <div class="receipt-title">INVOICE</div>
            <div class="address-line">Hindi Bazaar, Ghanta Ghar, Gorakhpur</div>
            <div class="address-line">Tel: 9119810269, 9956558465</div>
          </div>

          <div class="divider"></div>

          <div class="info-section">
            <div class="info-row">
              <span class="info-label">Date:</span>
              <span>${formattedDate}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Time:</span>
              <span>${formattedTime}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Invoice #:</span>
              <span>GC-${billNumber}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Customer:</span>
              <span>${bill.name || 'Customer'}</span>
            </div>
          </div>

          <div class="divider"></div>

          <table>
            <thead>
              <tr>
                <th>Item</th>
                <th class="qty-col">Qty</th>
                <th class="price-col">Price</th>
                <th class="amount-col">Amount</th>
              </tr>
            </thead>
            <tbody>
              ${bill.items.map((item, index) => `
                <tr>
                  <td>${item.name || `Item ${index + 1}`}</td>
                  <td class="qty-col">${item.quantity}</td>
                  <td class="price-col">${formatCurrencySync(item.price, currencySymbol)}</td>
                  <td class="amount-col">${formatCurrencySync(item.amount, currencySymbol)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <div class="summary-details">
            <div class="info-row">
              <span>Subtotal:</span>
              <span>${formatCurrencySync(bill.totalAmount, currencySymbol)}</span>
            </div>
            ${bill.discountAmount && bill.discountAmount > 0 ? `
              <div class="info-row">
                <span>Discount:</span>
                <span>- ${formatCurrencySync(bill.discountAmount, currencySymbol)}</span>
              </div>
            ` : ''}
            <div class="info-row grand-total">
            <span>TOTAL:</span>
              <span>${formatCurrencySync(bill.finalAmount ?? bill.totalAmount, currencySymbol)}</span>
            </div>
          </div>

          <div class="divider"></div>

          <div class="text-center barcode">
            *GC${billNumber}*
          </div>

          <div class="footer text-center">
            <div class="footer-text">Thank you for shopping at GIFT CORNER!</div>
            <div class="footer-text">All items sold are non-returnable</div>
          </div>
        </body>
      </html>
    `;
  }
};

// Function to print the bill
export const printBill = async (bill: Bill): Promise<void> => {
  const html = await generateBillHTML(bill);
  await Print.printAsync({
    html,
  });
};

// Function to share the bill - Optimized for speed
export const shareBill = async (bill: Bill): Promise<void> => {
  try {
    // Check if sharing is available first (faster)
    if (!(await Sharing.isAvailableAsync())) {
      throw new Error('Sharing is not available on this device');
    }

    // Generate simplified HTML for faster processing
    const html = await generateBillHTML(bill, 'pdf');

    // Create PDF with optimized settings for speed
    const { uri } = await Print.printToFileAsync({
      html,
      width: 612,
      height: 792,
      margins: { left: 20, top: 20, right: 20, bottom: 20 }
    });

    // Generate a PDF filename based on bill details
    const pdfFileName = `GC-Invoice-${bill.id.substring(0, 8)}.pdf`;

    // Copy to a more accessible location with meaningful name
    const pdfUri = `${FileSystem.documentDirectory}${pdfFileName}`;
    await FileSystem.copyAsync({
      from: uri,
      to: pdfUri
    });

    // Share the PDF
    await Sharing.shareAsync(pdfUri, {
      UTI: '.pdf',
      mimeType: 'application/pdf',
    });

    // Clean up temporary files
    await FileSystem.deleteAsync(uri, { idempotent: true });
  } catch (error) {
    console.error('Error sharing bill:', error);
    throw error;
  }
};