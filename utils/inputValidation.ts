/**
 * Comprehensive input validation and sanitization utilities
 * Prevents injection attacks, data corruption, and calculation errors
 */

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  value?: any;
  error?: string;
}

// Numeric validation options
export interface NumericValidationOptions {
  min?: number;
  max?: number;
  allowZero?: boolean;
  allowNegative?: boolean;
  maxDecimalPlaces?: number;
}

// String validation options
export interface StringValidationOptions {
  minLength?: number;
  maxLength?: number;
  allowEmpty?: boolean;
  pattern?: RegExp;
  sanitize?: boolean;
}

/**
 * Validate and sanitize numeric input for financial calculations
 */
export function validateNumericInput(
  input: string | number, 
  options: NumericValidationOptions = {}
): ValidationResult {
  const {
    min = 0,
    max = Number.MAX_SAFE_INTEGER,
    allowZero = true,
    allowNegative = false,
    maxDecimalPlaces = 2
  } = options;

  try {
    // Convert to string for processing
    const inputStr = String(input).trim();
    
    // Check for empty input
    if (!inputStr) {
      return { isValid: false, error: 'Input cannot be empty' };
    }

    // Check for invalid characters (only allow digits, decimal point, and minus sign)
    if (!/^-?\d*\.?\d*$/.test(inputStr)) {
      return { isValid: false, error: 'Input contains invalid characters' };
    }

    // Parse as number
    const numValue = parseFloat(inputStr);
    
    // Check if parsing resulted in NaN
    if (isNaN(numValue)) {
      return { isValid: false, error: 'Invalid numeric value' };
    }

    // Check if infinite
    if (!isFinite(numValue)) {
      return { isValid: false, error: 'Value cannot be infinite' };
    }

    // Check zero allowance
    if (numValue === 0 && !allowZero) {
      return { isValid: false, error: 'Zero is not allowed' };
    }

    // Check negative allowance
    if (numValue < 0 && !allowNegative) {
      return { isValid: false, error: 'Negative values are not allowed' };
    }

    // Check range
    if (numValue < min) {
      return { isValid: false, error: `Value must be at least ${min}` };
    }
    
    if (numValue > max) {
      return { isValid: false, error: `Value cannot exceed ${max}` };
    }

    // Check decimal places
    const decimalPlaces = (inputStr.split('.')[1] || '').length;
    if (decimalPlaces > maxDecimalPlaces) {
      return { isValid: false, error: `Maximum ${maxDecimalPlaces} decimal places allowed` };
    }

    // Round to specified decimal places to prevent floating point issues
    const roundedValue = Math.round(numValue * Math.pow(10, maxDecimalPlaces)) / Math.pow(10, maxDecimalPlaces);

    return { isValid: true, value: roundedValue };
  } catch (error) {
    return { isValid: false, error: `Validation error: ${error.message}` };
  }
}

/**
 * Validate and sanitize string input
 */
export function validateStringInput(
  input: string, 
  options: StringValidationOptions = {}
): ValidationResult {
  const {
    minLength = 0,
    maxLength = 1000,
    allowEmpty = false,
    pattern,
    sanitize = true
  } = options;

  try {
    let value = input;

    // Sanitize if requested
    if (sanitize) {
      // Remove potentially dangerous characters
      value = value.replace(/[<>\"'&]/g, '');
      // Trim whitespace
      value = value.trim();
    }

    // Check empty allowance
    if (!value && !allowEmpty) {
      return { isValid: false, error: 'Input cannot be empty' };
    }

    // Check length constraints
    if (value.length < minLength) {
      return { isValid: false, error: `Input must be at least ${minLength} characters` };
    }

    if (value.length > maxLength) {
      return { isValid: false, error: `Input cannot exceed ${maxLength} characters` };
    }

    // Check pattern if provided
    if (pattern && !pattern.test(value)) {
      return { isValid: false, error: 'Input format is invalid' };
    }

    return { isValid: true, value };
  } catch (error) {
    return { isValid: false, error: `Validation error: ${error.message}` };
  }
}

/**
 * Validate bill item data
 */
export function validateBillItem(item: any): ValidationResult {
  try {
    if (!item || typeof item !== 'object') {
      return { isValid: false, error: 'Invalid item data' };
    }

    // Validate quantity
    const quantityValidation = validateNumericInput(item.quantity, {
      min: 0.01,
      max: 999999,
      allowZero: false,
      maxDecimalPlaces: 3
    });
    
    if (!quantityValidation.isValid) {
      return { isValid: false, error: `Quantity: ${quantityValidation.error}` };
    }

    // Validate price
    const priceValidation = validateNumericInput(item.price, {
      min: 0.01,
      max: 999999,
      allowZero: false,
      maxDecimalPlaces: 2
    });
    
    if (!priceValidation.isValid) {
      return { isValid: false, error: `Price: ${priceValidation.error}` };
    }

    // Validate name if provided
    if (item.name) {
      const nameValidation = validateStringInput(item.name, {
        minLength: 1,
        maxLength: 100,
        allowEmpty: false
      });
      
      if (!nameValidation.isValid) {
        return { isValid: false, error: `Name: ${nameValidation.error}` };
      }
    }

    return { 
      isValid: true, 
      value: {
        ...item,
        quantity: quantityValidation.value,
        price: priceValidation.value,
        amount: quantityValidation.value * priceValidation.value
      }
    };
  } catch (error) {
    return { isValid: false, error: `Item validation error: ${error.message}` };
  }
}

/**
 * Validate discount input
 */
export function validateDiscountInput(
  value: string, 
  type: 'percentage' | 'absolute',
  subtotal: number = 0
): ValidationResult {
  try {
    if (type === 'percentage') {
      const validation = validateNumericInput(value, {
        min: 0,
        max: 100,
        allowZero: true,
        maxDecimalPlaces: 2
      });
      
      if (!validation.isValid) {
        return validation;
      }

      return { isValid: true, value: validation.value };
    } else {
      const validation = validateNumericInput(value, {
        min: 0,
        max: subtotal > 0 ? subtotal : Number.MAX_SAFE_INTEGER,
        allowZero: true,
        maxDecimalPlaces: 2
      });
      
      if (!validation.isValid) {
        return validation;
      }

      // Additional check: discount cannot exceed subtotal
      if (subtotal > 0 && validation.value > subtotal) {
        return { isValid: false, error: 'Discount cannot exceed subtotal' };
      }

      return { isValid: true, value: validation.value };
    }
  } catch (error) {
    return { isValid: false, error: `Discount validation error: ${error.message}` };
  }
}

/**
 * Sanitize URL input to prevent injection attacks
 */
export function sanitizeUrl(url: string): ValidationResult {
  try {
    const trimmedUrl = url.trim();
    
    if (!trimmedUrl) {
      return { isValid: false, error: 'URL cannot be empty' };
    }

    // Check for valid URL format
    try {
      const urlObj = new URL(trimmedUrl);
      
      // Only allow http and https protocols
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return { isValid: false, error: 'Only HTTP and HTTPS protocols are allowed' };
      }

      return { isValid: true, value: urlObj.toString() };
    } catch {
      return { isValid: false, error: 'Invalid URL format' };
    }
  } catch (error) {
    return { isValid: false, error: `URL validation error: ${error.message}` };
  }
}
