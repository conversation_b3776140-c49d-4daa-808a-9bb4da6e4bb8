import AsyncStorage from '@react-native-async-storage/async-storage';
import { Bill } from '../types/bill';
import { secureStorage, SECURE_STORAGE_KEYS } from './secureStorage';

const BILLS_STORAGE_KEY = 'gift_corner_bills';
// const BILLS_STORAGE_VERSION = 'v1'; // For future migrations if needed
const SHOW_ITEM_NAME_SETTING_KEY = 'gift_corner_show_item_name_setting';

// In-memory cache to improve performance - initialize as empty array instead of null
let billsCache: Bill[] = [];
let isCacheInitialized = false;

// Mutex-like mechanism to prevent race conditions
let storageOperationInProgress = false;
const pendingOperations: Array<() => Promise<void>> = [];

/**
 * Execute storage operations sequentially to prevent race conditions
 */
async function executeWithLock<T>(operation: () => Promise<T>): Promise<T> {
  return new Promise((resolve, reject) => {
    const wrappedOperation = async () => {
      try {
        const result = await operation();
        resolve(result);
      } catch (error) {
        reject(error);
      } finally {
        storageOperationInProgress = false;
        // Execute next pending operation
        const nextOperation = pendingOperations.shift();
        if (nextOperation) {
          storageOperationInProgress = true;
          nextOperation();
        }
      }
    };

    if (storageOperationInProgress) {
      // Queue the operation
      pendingOperations.push(wrappedOperation);
    } else {
      // Execute immediately
      storageOperationInProgress = true;
      wrappedOperation();
    }
  });
}

/**
 * Get bills with optimized caching and secure storage
 */
export const getBills = async (): Promise<Bill[]> => {
  return executeWithLock(async () => {
    try {
      // Return from cache if already initialized
      if (isCacheInitialized) {
        return [...billsCache]; // Return a copy to prevent mutation
      }

      // Try to load from secure storage first
      let bills: Bill[] = [];

      try {
        const secureBills = await secureStorage.getSecureItem(SECURE_STORAGE_KEYS.BILLS_DATA);
        if (secureBills && Array.isArray(secureBills)) {
          bills = secureBills;
        } else {
          // Fallback to AsyncStorage for migration
          const billsJSON = await AsyncStorage.getItem(BILLS_STORAGE_KEY);
          if (billsJSON) {
            bills = JSON.parse(billsJSON);
            // Migrate to secure storage
            await secureStorage.setSecureItem(SECURE_STORAGE_KEYS.BILLS_DATA, bills);
            // Remove from AsyncStorage after successful migration
            await AsyncStorage.removeItem(BILLS_STORAGE_KEY);
          }
        }
      } catch (secureError) {
        if (__DEV__) {
          console.warn('[Storage] Secure storage failed, falling back to AsyncStorage:', secureError);
        }
        // Fallback to AsyncStorage
        const billsJSON = await AsyncStorage.getItem(BILLS_STORAGE_KEY);
        bills = billsJSON ? JSON.parse(billsJSON) : [];
      }

      // Update cache
      billsCache = bills;
      isCacheInitialized = true;

      return [...billsCache];
    } catch (error) {
      if (__DEV__) {
        console.error('[Storage] Error getting bills:', error);
      }
      // Return empty array on error to ensure app works offline
      return [];
    }
  });
};

/**
 * Save a bill with optimized performance and secure storage
 */
export const saveBill = async (bill: Bill): Promise<void> => {
  return executeWithLock(async () => {
    try {
      // Validate bill data before saving
      if (!bill || !bill.id || typeof bill.totalAmount !== 'number') {
        throw new Error('Invalid bill data provided');
      }

      // Get existing bills (from cache if possible)
      const existingBills = isCacheInitialized ? [...billsCache] : await getBills();

      // Add new bill
      const updatedBills = [...existingBills, bill];

      // Update cache first for immediate UI updates
      billsCache = updatedBills;
      isCacheInitialized = true;

      // Persist to secure storage
      try {
        await secureStorage.setSecureItem(SECURE_STORAGE_KEYS.BILLS_DATA, updatedBills);
      } catch (secureError) {
        if (__DEV__) {
          console.warn('[Storage] Secure storage failed, falling back to AsyncStorage:', secureError);
        }
        // Fallback to AsyncStorage
        await AsyncStorage.setItem(BILLS_STORAGE_KEY, JSON.stringify(updatedBills));
      }
    } catch (error) {
      if (__DEV__) {
        console.error('[Storage] Error saving bill:', error);
      }
      throw new Error(`Failed to save bill: ${error.message}`);
    }
  });
};

/**
 * Delete a bill with optimized performance and secure storage
 */
export const deleteBill = async (billId: string): Promise<void> => {
  return executeWithLock(async () => {
    try {
      // Validate input
      if (!billId || typeof billId !== 'string') {
        throw new Error('Invalid bill ID provided');
      }

      // Use cache if available, otherwise fetch
      const existingBills = isCacheInitialized ? [...billsCache] : await getBills();

      const updatedBills = existingBills.filter(bill => bill.id !== billId);

      // Check if bill was actually found and removed
      if (updatedBills.length === existingBills.length) {
        throw new Error(`Bill with ID ${billId} not found`);
      }

      // Update cache first
      billsCache = updatedBills;

      // Persist to secure storage
      try {
        await secureStorage.setSecureItem(SECURE_STORAGE_KEYS.BILLS_DATA, updatedBills);
      } catch (secureError) {
        if (__DEV__) {
          console.warn('[Storage] Secure storage failed, falling back to AsyncStorage:', secureError);
        }
        // Fallback to AsyncStorage
        await AsyncStorage.setItem(BILLS_STORAGE_KEY, JSON.stringify(updatedBills));
      }
    } catch (error) {
      if (__DEV__) {
        console.error('[Storage] Error deleting bill:', error);
      }
      throw new Error(`Failed to delete bill: ${error.message}`);
    }
  });
};

/**
 * Update an existing bill with secure storage
 */
export const updateBill = async (updatedBill: Bill): Promise<void> => {
  return executeWithLock(async () => {
    try {
      // Validate input
      if (!updatedBill || !updatedBill.id || typeof updatedBill.totalAmount !== 'number') {
        throw new Error('Invalid updated bill data provided');
      }

      // Use cache if available, otherwise fetch
      const existingBills = isCacheInitialized ? [...billsCache] : await getBills();

      const billIndex = existingBills.findIndex(bill => bill.id === updatedBill.id);
      if (billIndex === -1) {
        throw new Error(`Bill with ID ${updatedBill.id} not found`);
      }

      // Create updated bills array
      const updatedBills = [...existingBills];
      updatedBills[billIndex] = updatedBill;

      // Update cache first for immediate UI updates
      billsCache = updatedBills;

      // Persist to secure storage
      try {
        await secureStorage.setSecureItem(SECURE_STORAGE_KEYS.BILLS_DATA, updatedBills);
      } catch (secureError) {
        if (__DEV__) {
          console.warn('[Storage] Secure storage failed, falling back to AsyncStorage:', secureError);
        }
        // Fallback to AsyncStorage
        await AsyncStorage.setItem(BILLS_STORAGE_KEY, JSON.stringify(updatedBills));
      }
    } catch (error) {
      if (__DEV__) {
        console.error('[Storage] Error updating bill:', error);
      }
      throw new Error(`Failed to update bill: ${error.message}`);
    }
  });
};

/**
 * Clear all bills from storage (secure and regular)
 */
export const clearAllBills = async (): Promise<void> => {
  return executeWithLock(async () => {
    try {
      // Clear cache first
      billsCache = [];
      isCacheInitialized = false;

      // Remove from both secure storage and AsyncStorage
      try {
        await secureStorage.removeSecureItem(SECURE_STORAGE_KEYS.BILLS_DATA);
      } catch (secureError) {
        if (__DEV__) {
          console.warn('[Storage] Failed to clear secure storage:', secureError);
        }
      }

      // Also remove from AsyncStorage (for migration cleanup)
      try {
        await AsyncStorage.removeItem(BILLS_STORAGE_KEY);
      } catch (asyncError) {
        if (__DEV__) {
          console.warn('[Storage] Failed to clear AsyncStorage:', asyncError);
        }
      }
    } catch (error) {
      if (__DEV__) {
        console.error('[Storage] Error clearing bills:', error);
      }
      throw new Error(`Failed to clear bills: ${error.message}`);
    }
  });
};

/**
 * Clear cache to force reload from storage
 */
export const clearCache = (): void => {
  billsCache = [];
  isCacheInitialized = false;
};

/**
 * Saves the user's preference for showing item name input in the new bill screen.
 * @param value boolean - true to show item name input, false to hide.
 */
export const saveShowItemNameSetting = async (value: boolean): Promise<void> => {
  try {
    await AsyncStorage.setItem(SHOW_ITEM_NAME_SETTING_KEY, JSON.stringify(value));
  } catch (error) {
    if (__DEV__) {
      console.error('Error saving show item name setting:', error);
    }
    throw error; // Rethrow to allow caller to handle or display message
  }
};

/**
 * Loads the user's preference for showing item name input.
 * @returns Promise<boolean> - true if item name input should be shown, false otherwise. Defaults to false.
 */
export const loadShowItemNameSetting = async (): Promise<boolean> => {
  try {
    const valueJSON = await AsyncStorage.getItem(SHOW_ITEM_NAME_SETTING_KEY);
    if (valueJSON !== null) {
      return JSON.parse(valueJSON);
    }
    return false; // Default to false if no setting is found
  } catch (error) {
    if (__DEV__) {
      console.error('Error loading show item name setting:', error);
    }
    return false; // Default to false on error
  }
};