import { useEffect, useState, useCallback, useMemo } from 'react'
import { database, billsCollection, customersCollection, itemsCollection, paymentsCollection } from '../database'
import { Q } from '@nozbe/watermelondb'

export function useDatabase() {
  const [isReady, setIsReady] = useState(false)
  const [error, setError] = useState(null)

  useEffect(() => {
    let isMounted = true

    const initDatabase = async () => {
      try {
        await database.write(async () => {
          // You can add initial data here if needed
        })

        if (isMounted) {
          setIsReady(true)
          setError(null)
        }
      } catch (error) {
        if (__DEV__) {
          console.error('Failed to initialize database:', error)
        }

        if (isMounted) {
          setError(error)
          setIsReady(false)
        }
      }
    }

    initDatabase()

    return () => {
      isMounted = false
    }
  }, [])

  return {
    database,
    isReady,
    error,
    collections: {
      bills: billsCollection,
      customers: customersCollection,
      items: itemsCollection,
      payments: paymentsCollection
    }
  }
}

// Hook to observe a collection with optional query builder
export function useCollection(collection, queryBuilder = null) {
  const [data, setData] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)

  // Memoize the query to prevent unnecessary rebuilding
  const query = useMemo(() => {
    try {
      if (queryBuilder) {
        return queryBuilder(collection)
      }
      return collection.query()
    } catch (err) {
      if (__DEV__) {
        console.error('Error building query:', err)
      }
      setError(err)
      return null
    }
  }, [collection, queryBuilder])

  useEffect(() => {
    if (!query) {
      setIsLoading(false)
      return
    }

    let isMounted = true
    setIsLoading(true)
    setError(null)

    const subscription = query.observe().subscribe({
      next: (results) => {
        if (isMounted) {
          setData(results)
          setIsLoading(false)
          setError(null)
        }
      },
      error: (err) => {
        if (__DEV__) {
          console.error('Error observing collection:', err)
        }
        if (isMounted) {
          setError(err)
          setIsLoading(false)
        }
      }
    })

    return () => {
      isMounted = false
      subscription?.unsubscribe()
    }
  }, [query])

  return { data, isLoading, error }
}

// Hook to observe a collection with pagination
export function usePaginatedCollection(collection, queryBuilder = null, pageSize = 20) {
  const [data, setData] = useState([])
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(1)
  const [isLoading, setIsLoading] = useState(true)
  const [totalCount, setTotalCount] = useState(0)

  // Memoize the query to prevent unnecessary rebuilding
  const query = useMemo(() => {
    if (queryBuilder) {
      return queryBuilder(collection)
    }
    return collection.query()
  }, [collection, queryBuilder])

  // Load initial data
  useEffect(() => {
    setIsLoading(true)
    setPage(1)
    setData([])
    setHasMore(true)

    // First, get the total count
    const loadTotalCount = async () => {
      try {
        const allResults = await query.fetch()
        setTotalCount(allResults.length)
      } catch (error) {
        if (__DEV__) {
          console.error('Error fetching total count:', error)
        }
      }
    }

    loadTotalCount()

    // Then load the first page
    const loadInitialData = async () => {
      try {
        const results = await query.extend(Q.take(pageSize)).fetch()

        setData(results)
        setHasMore(results.length === pageSize)
        setIsLoading(false)
      } catch (error) {
        if (__DEV__) {
          console.error('Error loading paginated data:', error)
        }
        setIsLoading(false)
      }
    }

    loadInitialData()
  }, [query, pageSize])

  // Function to load the next page
  const loadMore = useCallback(async () => {
    if (!hasMore || isLoading) return

    setIsLoading(true)

    try {
      const nextPage = page + 1
      const skipCount = (nextPage - 1) * pageSize

      const results = await query
        .extend(Q.skip(skipCount), Q.take(pageSize))
        .fetch()

      if (results.length > 0) {
        setData(prevData => [...prevData, ...results])
        setPage(nextPage)
        setHasMore(results.length === pageSize)
      } else {
        setHasMore(false)
      }
    } catch (error) {
      if (__DEV__) {
        console.error('Error loading more data:', error)
      }
    } finally {
      setIsLoading(false)
    }
  }, [query, page, pageSize, hasMore, isLoading])

  return {
    data,
    isLoading,
    hasMore,
    loadMore,
    page,
    totalCount,
    totalPages: Math.ceil(totalCount / pageSize)
  }
}

// Hook to observe a single record with all relations preloaded
export function useRecordWithRelations(collection, id, relations = []) {
  const [record, setRecord] = useState(null)
  const [relatedData, setRelatedData] = useState({})
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (!id) {
      setIsLoading(false)
      return
    }

    let subscription

    const fetchRecord = async () => {
      try {
        // First observe the main record
        subscription = collection.findAndObserve(id).subscribe(async result => {
          setRecord(result)

          // If relations are specified, load them
          if (relations.length > 0) {
            const relatedResults = {}

            // Load all relations in parallel
            await Promise.all(relations.map(async relation => {
              try {
                const data = await result[relation].fetch()
                relatedResults[relation] = data
              } catch (err) {
                if (__DEV__) {
                  console.error(`Error fetching relation ${relation}:`, err)
                }
              }
            }))

            setRelatedData(relatedResults)
          }

          setIsLoading(false)
        })
      } catch (err) {
        if (__DEV__) {
          console.error('Error finding record:', err)
        }
        setError(err)
        setIsLoading(false)
      }
    }

    fetchRecord()

    return () => subscription?.unsubscribe()
  }, [collection, id, relations])

  return { record, relatedData, isLoading, error }
}

// Hook to observe a single record
export function useRecord(collection, id) {
  const [record, setRecord] = useState(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!id) {
      setIsLoading(false)
      return
    }

    const subscription = collection.findAndObserve(id).subscribe(result => {
      setRecord(result)
      setIsLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [collection, id])

  return { record, isLoading }
}