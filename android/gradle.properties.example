# Production Signing Configuration
# Copy this file to gradle.properties and fill in your actual values
# DO NOT commit gradle.properties with real credentials to version control

# Release keystore configuration
MYAPP_UPLOAD_STORE_FILE=my-upload-key.keystore
MYAPP_UPLOAD_KEY_ALIAS=my-key-alias
MYAPP_UPLOAD_STORE_PASSWORD=*****
MYAPP_UPLOAD_KEY_PASSWORD=*****

# Instructions for generating production keystore:
# 1. Generate keystore: keytool -genkeypair -v -storetype PKCS12 -keystore my-upload-key.keystore -alias my-key-alias -keyalg RSA -keysize 2048 -validity 10000
# 2. Place the keystore file in android/app/ directory
# 3. Copy this file to gradle.properties and fill in the actual passwords
# 4. Add gradle.properties to .gitignore to prevent credential exposure

# Security Notes:
# - Use strong passwords (minimum 12 characters with mixed case, numbers, symbols)
# - Store keystore file securely and create backups
# - Never commit actual credentials to version control
# - Consider using environment variables in CI/CD pipelines
